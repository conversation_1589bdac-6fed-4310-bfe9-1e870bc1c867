<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition, ProductInfo } from '@/types';
import { PositionEffectEnum, TradeDirectionEnum } from '@/enum';
import { RecordService, TradingService } from '@/api';
import { getColorClass, putRow } from '@/script';
import type { SocketDataPackage, TradeRecordInfo } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : ProductInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<TradeRecordInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 80,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 100,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = TradeDirectionEnum[cellData];
      return <span class={getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '开平',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return <span>{PositionEffectEnum[cellData]}</span>;
    },
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'volume',
    dataKey: 'volume',
    title: '成交量',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'tradeTime',
    dataKey: 'tradeTime',
    title: '成交时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'tradeId',
    dataKey: 'tradeId',
    title: '成交编号',
    width: 120,
    sortable: true,
  },
  {
    key: 'exchangeOrderId',
    dataKey: 'exchangeOrderId',
    title: '交易所编号',
    width: 120,
    sortable: true,
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<TradeRecordInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 成交数据
const tradeRecords = shallowRef<TradeRecordInfo[]>([]);

// 监听账户/产品变化，重新获取成交数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchTradeRecords();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchTradeRecords();
  }
  TradingService.subscribePositionChange(handleRecordChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribePositionChange(handleRecordChange);
});

/** 监听成交变化 */
const handleRecordChange = (data: SocketDataPackage<TradeRecordInfo>) => {
  const { body } = data;
  if (body) {
    putRow(body, tradeRecords, 'tradeId');
  }
};

// 获取成交数据
const fetchTradeRecords = async () => {
  if (!activeItem) return;
  tradeRecords.value = await RecordService.getTodayTrades(activeItem.id);
};
</script>

<template>
  <VirtualizedTable
    :columns="columns"
    :data="tradeRecords"
    identity="tradeId"
    :sort="{ key: 'tradeTime', order: TableV2SortOrder.DESC }"
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchTradeRecords" size="small" color="var(--g-primary)">刷新</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
