<script setup lang="tsx">
import { ref, shallowRef, computed } from 'vue';
import { ElMessage, ElTag, ElIcon, TableV2SortOrder } from 'element-plus';
import type { MomUser } from '../../../../xtrade-sdk/dist';
import type { ColumnDefinition } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 操作日志数据类型
interface OperationLog {
  id: number;
  time: string;
  operation: string;
  operationType: 'login' | 'logout' | 'trade' | 'query' | 'setting';
  details: string;
  ip: string;
  mac: string;
  result: 'success' | 'failed';
  userAgent: string;
}

// 操作日志数据
const operationLogs = shallowRef<OperationLog[]>([
  {
    id: 1,
    time: '2025/05/18 13:48:22',
    operation: '登录',
    operationType: 'login',
    details: '用户登录系统',
    ip: '192.473.117.114',
    mac: '3c:a6:f6:aa:0f:c0',
    result: 'success',
    userAgent: 'Chrome *********',
  },
  {
    id: 2,
    time: '2025/05/18 13:48:22',
    operation: '交易',
    operationType: 'trade',
    details: '买入 ABC234 1000股',
    ip: '192.473.117.114',
    mac: '3c:a6:f6:aa:0f:c0',
    result: 'success',
    userAgent: 'Chrome *********',
  },
  {
    id: 3,
    time: '2025/05/18 13:48:22',
    operation: '交易',
    operationType: 'trade',
    details: '卖出 DEF567 500股',
    ip: '192.473.117.114',
    mac: '3c:a6:f6:aa:0f:c0',
    result: 'failed',
    userAgent: 'Chrome *********',
  },
  {
    id: 4,
    time: '2025/05/18 13:48:22',
    operation: '查询',
    operationType: 'query',
    details: '查询持仓信息',
    ip: '192.473.117.114',
    mac: '3c:a6:f6:aa:0f:c0',
    result: 'success',
    userAgent: 'Chrome *********',
  },
  {
    id: 5,
    time: '2025/05/18 13:48:22',
    operation: '登出',
    operationType: 'logout',
    details: '用户主动登出',
    ip: '192.473.117.114',
    mac: '3c:a6:f6:aa:0f:c0',
    result: 'success',
    userAgent: 'Chrome *********',
  },
]);

// 筛选条件
const filters = ref({
  dateRange: [] as string[],
  operationType: '',
  result: '',
  keyword: '',
});

// 操作类型选项
const operationTypes = [
  { label: '全部', value: '' },
  { label: '登录', value: 'login' },
  { label: '登出', value: 'logout' },
  { label: '交易', value: 'trade' },
  { label: '查询', value: 'query' },
  { label: '设置', value: 'setting' },
];

// 结果类型选项
const resultTypes = [
  { label: '全部', value: '' },
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' },
];

// 结果状态映射
const resultMap = {
  success: { label: '成功', color: 'success' as const },
  failed: { label: '失败', color: 'danger' as const },
};

// 操作类型映射
const operationTypeMap = {
  login: { label: '登录', icon: 'User', color: 'primary' },
  logout: { label: '登出', icon: 'SwitchButton', color: 'info' },
  trade: { label: '交易', icon: 'TrendCharts', color: 'warning' },
  query: { label: '查询', icon: 'Search', color: 'success' },
  setting: { label: '设置', icon: 'Setting', color: 'info' },
};

// 表格列定义
const columns: ColumnDefinition<OperationLog> = [
  {
    key: 'time',
    title: '时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'operation',
    title: '操作',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: OperationLog }) => {
      const typeInfo = operationTypeMap[rowData.operationType];
      return (
        <div class="flex items-center gap-2">
          <ElIcon class={`text-${typeInfo.color}-500`}>
            <component is={typeInfo.icon} />
          </ElIcon>
          <span>{rowData.operation}</span>
        </div>
      );
    },
  },
  {
    key: 'details',
    title: '详情',
    width: 300,
    cellRenderer: ({ rowData }: { rowData: OperationLog }) => (
      <span class="text-gray-600">{rowData.details}</span>
    ),
  },
  {
    key: 'result',
    title: '结果',
    width: 80,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: OperationLog }) => {
      const resultInfo = resultMap[rowData.result];
      return (
        <ElTag type={resultInfo.color} size="small">
          {resultInfo.label}
        </ElTag>
      );
    },
  },
  {
    key: 'ip',
    title: 'IP地址',
    width: 120,
    sortable: true,
  },
  {
    key: 'mac',
    title: 'MAC地址',
    width: 140,
    sortable: true,
  },
  {
    key: 'userAgent',
    title: '用户代理',
    width: 150,
    cellRenderer: ({ rowData }: { rowData: OperationLog }) => (
      <span class="text-gray-500 text-sm">{rowData.userAgent}</span>
    ),
  },
];

// 自定义过滤函数
const customFilter = (log: OperationLog) => {
  // 按日期范围筛选
  if (filters.value.dateRange.length === 2) {
    const [startDate, endDate] = filters.value.dateRange;
    const logDate = new Date(log.time).getTime();
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime() + 24 * 60 * 60 * 1000; // 包含结束日期
    if (!(logDate >= start && logDate < end)) {
      return false;
    }
  }

  // 按操作类型筛选
  if (filters.value.operationType && log.operationType !== filters.value.operationType) {
    return false;
  }

  // 按结果筛选
  if (filters.value.result && log.result !== filters.value.result) {
    return false;
  }

  // 按关键词筛选
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase();
    const matchesKeyword =
      log.operation.toLowerCase().includes(keyword) ||
      log.details.toLowerCase().includes(keyword) ||
      log.ip.includes(keyword) ||
      log.mac.toLowerCase().includes(keyword);
    if (!matchesKeyword) {
      return false;
    }
  }

  return true;
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    dateRange: [],
    operationType: '',
    result: '',
    keyword: '',
  };
};

// 导出日志
const exportLogs = () => {
  // 这里应该调用API导出日志
  console.log('导出操作日志:', operationLogs.value);
  ElMessage.success('日志导出成功');
};

// 刷新日志
const refreshLogs = () => {
  // 这里应该调用API刷新日志数据
  console.log('刷新操作日志');
  ElMessage.success('日志已刷新');
};
</script>

<template>
  <div p-6>
    <div mb-4 flex="~ items-center justify-between">
      <div>
        <h3 text-lg font-medium mb-2>操作日志</h3>
        <p text-sm text-gray-600>查看用户的操作记录和行为轨迹</p>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card mb-4>
      <el-form :model="filters" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="操作类型">
          <el-select v-model="filters.operationType" w-32>
            <el-option
              v-for="type in operationTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="执行结果">
          <el-select v-model="filters.result" w-24>
            <el-option
              v-for="result in resultTypes"
              :key="result.value"
              :label="result.label"
              :value="result.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关键词">
          <el-input v-model="filters.keyword" placeholder="搜索操作、IP、MAC等" w-48 clearable />
        </el-form-item>

        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作日志表格 -->
    <VirtualizedTable
      :sort="{ key: 'time', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="operationLogs"
      :custom-filter="customFilter"
      search-placeholder="搜索操作、IP、MAC等"
      :row-height="50"
      fixed
    >
      <template #actions>
        <div flex="~ items-center gap-3">
          <el-button @click="refreshLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="exportLogs">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
