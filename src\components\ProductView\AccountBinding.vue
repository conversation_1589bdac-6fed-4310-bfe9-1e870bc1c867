<template>
  <div class="account-binding-view product-guide-slide" pr-10 flex flex-col>
    <template v-if="is4ExistingFund">
      <div pl-10 lh-48 fs-20 fw-400 color="[--g-text-color-5]">已绑定账号</div>
      <div class="upper">
        <AccountList v-bind:contextual="boundedContext" @unbind="unbindAccounts" />
      </div>
      <div mt-3 pl-10 lh-48 fs-20 fw-400 color="[--g-text-color-5]">可绑定账号</div>
      <div class="lower">
        <AccountList v-bind:contextual="availableContext" @bind="bindAccounts" />
      </div>
    </template>
    <div v-else h-full flex jcc aic>
      <span fs-20 fw-400 color="[--g-text-color-5]">请先填写并保存产品信息...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import AccountList from '../AccountView/AccountList.vue';
import type { ProductInfo } from '@/types';
import { isNotNone } from '@/script';
import type { LegacyAccountInfo } from '../../../../xtrade-sdk/dist';

const contextProduct = defineModel<ProductInfo | null>({});

const contextual = computed(() => {
  const { id, fundName, accounts } = contextProduct.value || { fundId: null, fundName: null };
  return { fundId: id || null, fundName, accounts: accounts || [] };
});

const is4ExistingFund = computed(() => {
  return isNotNone(contextual.value.fundId);
});

const boundedContext = computed(() => {
  const { fundId, fundName, accounts } = contextual.value;
  return {
    fundId,
    fundName,
    isBounded: true,
    boundedAccountIds: accounts.map(x => x.accountId) as any[],
  };
});

const availableContext = computed(() => {
  const { fundId, fundName } = contextual.value;
  return { fundId, fundName, isAvailable: true };
});

const emitter = defineEmits<{
  bind: [data: LegacyAccountInfo[]];
  unbind: [data: LegacyAccountInfo[]];
}>();

function unbindAccounts(accounts: LegacyAccountInfo[]) {
  emitter('unbind', accounts);
}

function bindAccounts(accounts: LegacyAccountInfo[]) {
  emitter('bind', accounts);
}

onMounted(() => {
  //
});
</script>

<style scoped>
.account-binding-view {
  .upper,
  .lower {
    height: 50%;
    flex-shrink: 1;
    flex-grow: 1;
    overflow: hidden;
  }
}
</style>
