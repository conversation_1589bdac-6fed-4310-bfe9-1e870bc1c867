<script setup lang="ts">
import { formatNumber, getColorClass } from '@/script';
import { computed, onMounted, shallowRef, watch, ref } from 'vue';
import { Repos } from '../../../../xtrade-sdk/dist';

import type {
  AccountInfo,
  FinanceAccountDetail,
  FinanceAccountDetailField,
  TradeChannel,
} from '@/types';

const repoInstance = new Repos.GovernanceRepo();

const { activeChannel } = defineProps<{
  activeChannel: TradeChannel;
}>();

// 定义账户字段，用于展示账户信息
const accountFields: FinanceAccountDetailField[][] = [
  [
    {
      key: 'balance',
      label: '权益',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
    {
      key: 'marketValue',
      label: '市值',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
  ],
  [
    {
      key: 'available',
      label: '可用资金',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
    {
      key: 'preBalance',
      label: '昨日权益',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
  ],
  [
    { key: 'nav', label: '净值', format: val => formatNumber(val, {}) },
    {
      key: 'profitRatio',
      label: '涨跌幅',
      format: val =>
        formatNumber(val, {
          percent: true,
        }),
      hasColor: true,
    },
  ],
  [
    {
      key: 'frozenMargin',
      label: '冻结资金',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
    {
      key: 'closeProfit',
      label: '平仓盈亏',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
      hasColor: true,
    },
  ],
  [
    {
      key: 'positionProfit',
      label: '浮动盈亏',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
      hasColor: true,
    },
    {
      key: 'commission',
      label: '手续费',
      format: val =>
        formatNumber(val, {
          separator: true,
        }),
    },
  ],
];

const currentAccountId = ref<string>();
const activeAccount = defineModel<FinanceAccountDetail>();
const accounts = shallowRef<AccountInfo[]>([]);

// 显示账户列表，根据渠道资产类型过滤，对于融资融券渠道，只显示有融资融券权限的账户
const displayAccounts = computed(() => {
  return accounts.value.filter(
    x => activeChannel.assetTypes.includes(x.assetType) && !activeChannel.credit,
  );
});

// 当渠道变化时，自动选择第一个账户
const handleActiveChannelChange = () => {
  if (displayAccounts.value.length > 0) {
    currentAccountId.value = displayAccounts.value[0].id;
  } else {
    currentAccountId.value = undefined;
  }
};

watch(() => activeChannel, handleActiveChannelChange);

// 当前账户变化时，自动选择对应的账户
watch(currentAccountId, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    activeAccount.value = accounts.value.find(account => account.id === newValue)! as any; // todo25
  }
});

// 加载账户列表
onMounted(async () => {
  accounts.value = (await repoInstance.QueryAccounts()).data || [];
  handleActiveChannelChange();
});

const getClass = (field: FinanceAccountDetailField) => {
  if (field.hasColor) {
    const val = activeAccount.value ? activeAccount.value[field.key] : '';
    return getColorClass(val);
  }
  return 'text-white';
};
</script>

<template>
  <div p="20px">
    <div flex items="center" mb-10>
      <div w-50>账号</div>
      <el-select placeholder="请选择账号" v-model="currentAccountId" filterable>
        <el-option
          v-for="item in displayAccounts"
          :key="item.id"
          :label="item.accountName"
          :value="item.id"
        />
      </el-select>
    </div>

    <div v-if="activeAccount">
      <div
        v-for="(row, rowIndex) in accountFields"
        :key="rowIndex"
        flex
        :mb="rowIndex === accountFields.length - 1 ? '0' : '8'"
      >
        <div
          v-for="(field, fieldIndex) in row"
          :key="field.key"
          flex
          flex-1
          jcsb
          :pr="fieldIndex % 2 === 0 ? '20' : '0'"
        >
          <div mr-4>{{ field.label }}：</div>
          <div :class="getClass(field)">
            {{ field.format(activeAccount[field.key]) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped></style>
