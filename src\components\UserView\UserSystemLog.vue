<script setup lang="tsx">
import { ref, shallowRef, computed } from 'vue';
import { ElMessage, ElTag, ElIcon, TableV2SortOrder } from 'element-plus';
import type { MomUser } from '../../../../xtrade-sdk/dist';
import type { ColumnDefinition } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 登录日志数据类型
interface SystemLog {
  id: number;
  time: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  module: string;
  message: string;
  details: string;
  source: string;
  traceId: string;
}

// 登录日志数据
const systemLogs = shallowRef<SystemLog[]>([
  {
    id: 1,
    time: '2025/05/18 13:48:22',
    level: 'info',
    module: '用户管理',
    message: '用户登录成功',
    details: '用户 admin 从 192.473.117.114 登录系统',
    source: 'auth-service',
    traceId: 'trace-001',
  },
  {
    id: 2,
    time: '2025/05/18 13:48:22',
    level: 'warn',
    module: '交易系统',
    message: '交易风控预警',
    details: '用户持仓比例超过80%，触发风控预警',
    source: 'trade-service',
    traceId: 'trace-002',
  },
  {
    id: 3,
    time: '2025/05/18 13:48:22',
    level: 'error',
    module: '数据同步',
    message: '数据同步失败',
    details: '与第三方数据源同步时发生网络超时错误',
    source: 'sync-service',
    traceId: 'trace-003',
  },
  {
    id: 4,
    time: '2025/05/18 13:48:22',
    level: 'debug',
    module: '权限验证',
    message: '权限验证通过',
    details: '用户访问产品列表页面，权限验证通过',
    source: 'auth-service',
    traceId: 'trace-004',
  },
  {
    id: 5,
    time: '2025/05/18 13:48:22',
    level: 'info',
    module: '系统监控',
    message: '系统状态正常',
    details: 'CPU使用率: 45%, 内存使用率: 60%, 磁盘使用率: 30%',
    source: 'monitor-service',
    traceId: 'trace-005',
  },
]);

// 筛选条件
const filters = ref({
  dateRange: [] as string[],
  level: '',
  module: '',
  keyword: '',
});

// 日志级别选项
const logLevels = [
  { label: '全部', value: '' },
  { label: 'DEBUG', value: 'debug' },
  { label: 'INFO', value: 'info' },
  { label: 'WARN', value: 'warn' },
  { label: 'ERROR', value: 'error' },
];

// 模块选项
const modules = [
  { label: '全部', value: '' },
  { label: '用户管理', value: '用户管理' },
  { label: '交易系统', value: '交易系统' },
  { label: '数据同步', value: '数据同步' },
  { label: '权限验证', value: '权限验证' },
  { label: '系统监控', value: '系统监控' },
];

// 日志级别映射
const levelMap = {
  debug: { label: 'DEBUG', color: 'info', bgColor: 'bg-gray-100', textColor: 'text-gray-600' },
  info: { label: 'INFO', color: 'primary', bgColor: 'bg-blue-100', textColor: 'text-blue-600' },
  warn: { label: 'WARN', color: 'warning', bgColor: 'bg-yellow-100', textColor: 'text-yellow-600' },
  error: { label: 'ERROR', color: 'danger', bgColor: 'bg-red-100', textColor: 'text-red-600' },
};

// 获取日志级别图标
const getLevelIcon = (level: string) => {
  switch (level) {
    case 'debug':
      return 'Bug';
    case 'info':
      return 'InfoFilled';
    case 'warn':
      return 'Warning';
    case 'error':
      return 'CircleCloseFilled';
    default:
      return 'Document';
  }
};

// 表格列定义
const columns: ColumnDefinition<SystemLog> = [
  {
    key: 'time',
    title: '时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'level',
    title: '级别',
    width: 80,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: SystemLog }) => {
      const levelInfo = levelMap[rowData.level];
      return (
        <div class="flex items-center gap-2">
          <ElIcon class={levelInfo.textColor}>
            <component is={getLevelIcon(rowData.level)} />
          </ElIcon>
          <span
            class={`px-2 py-1 rounded text-xs font-medium ${levelInfo.bgColor} ${levelInfo.textColor}`}
          >
            {levelInfo.label}
          </span>
        </div>
      );
    },
  },
  {
    key: 'module',
    title: '模块',
    width: 120,
    sortable: true,
  },
  {
    key: 'message',
    title: '消息',
    width: 200,
    cellRenderer: ({ rowData }: { rowData: SystemLog }) => (
      <span class="font-medium">{rowData.message}</span>
    ),
  },
  {
    key: 'details',
    title: '详情',
    width: 300,
    cellRenderer: ({ rowData }: { rowData: SystemLog }) => (
      <span class="text-gray-600">{rowData.details}</span>
    ),
  },
  {
    key: 'source',
    title: '来源',
    width: 120,
    sortable: true,
  },
  {
    key: 'traceId',
    title: '追踪ID',
    width: 120,
    cellRenderer: ({ rowData }: { rowData: SystemLog }) => (
      <span class="text-gray-500 font-mono text-sm">{rowData.traceId}</span>
    ),
  },
];

// 自定义过滤函数
const customFilter = (log: SystemLog) => {
  // 按日期范围筛选
  if (filters.value.dateRange.length === 2) {
    const [startDate, endDate] = filters.value.dateRange;
    const logDate = new Date(log.time).getTime();
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime() + 24 * 60 * 60 * 1000;
    if (!(logDate >= start && logDate < end)) {
      return false;
    }
  }

  // 按日志级别筛选
  if (filters.value.level && log.level !== filters.value.level) {
    return false;
  }

  // 按模块筛选
  if (filters.value.module && log.module !== filters.value.module) {
    return false;
  }

  // 按关键词筛选
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase();
    const matchesKeyword =
      log.message.toLowerCase().includes(keyword) ||
      log.details.toLowerCase().includes(keyword) ||
      log.source.toLowerCase().includes(keyword) ||
      log.traceId.toLowerCase().includes(keyword);
    if (!matchesKeyword) {
      return false;
    }
  }

  return true;
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    dateRange: [],
    level: '',
    module: '',
    keyword: '',
  };
};

// 导出日志
const exportLogs = () => {
  console.log('导出登录日志:', systemLogs.value);
  ElMessage.success('登录日志导出成功');
};

// 刷新日志
const refreshLogs = () => {
  console.log('刷新登录日志');
  ElMessage.success('登录日志已刷新');
};
</script>

<template>
  <div p-6>
    <div mb-4 flex="~ items-center justify-between">
      <div>
        <h3 text-lg font-medium mb-2>登录日志</h3>
        <p text-sm text-gray-600>查看与用户相关的系统运行日志和错误信息</p>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card mb-4>
      <el-form :model="filters" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="日志级别">
          <el-select v-model="filters.level" w-32>
            <el-option
              v-for="level in logLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="模块">
          <el-select v-model="filters.module" w-32>
            <el-option
              v-for="module in modules"
              :key="module.value"
              :label="module.label"
              :value="module.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关键词">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索消息、来源、追踪ID等"
            w-48
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 登录日志表格 -->
    <VirtualizedTable
      :sort="{ key: 'time', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="systemLogs"
      :custom-filter="customFilter"
      search-placeholder="搜索消息、来源、追踪ID等"
      :row-height="50"
      fixed
    >
      <template #actions>
        <div flex="~ items-center gap-3">
          <el-button @click="refreshLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="exportLogs">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
