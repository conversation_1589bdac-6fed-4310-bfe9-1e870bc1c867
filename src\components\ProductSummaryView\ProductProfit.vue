<script setup lang="ts">
import { computed, onMounted, shallowRef, watch } from 'vue';
import { formatNumber, getColorClass } from '@/script';
import type { ProductInfo } from '@/types';
import VueEchart from '../common/VueEchart.vue';
import type { EChartsOption } from 'echarts';
import type { CallbackDataParams } from 'echarts/types/dist/shared';

const { product } = defineProps<{
  product?: ProductInfo;
}>();

// 产品详情
const productDetail = shallowRef<ProductInfo | null>(null);

const accounts = computed(() => {
  const arr = productDetail.value?.fundAccounts ?? [];
  // 按positionProfit排序
  arr.sort((a, b) => {
    if (a.positionProfit === b.positionProfit) return 0;
    return a.positionProfit > b.positionProfit ? -1 : 1;
  });
  return arr;
});

// 饼图数据
const pieChartData = computed(() => {
  return accounts.value.map(account => ({
    name: account.accountName,
    value: account.positionProfit,
  }));
});

// 饼图配置
const pieChartOption = computed<EChartsOption>(() => {
  return {
    tooltip: {
      confine: true,
      formatter: params => {
        const p = params as CallbackDataParams;
        let result = '';
        result += `<div style="font-weight:bold">${p.name}</div>`;
        result += `<div >${formatNumber(p.value, { separator: true })}</div>`;
        return result;
      },
    },
    series: [
      {
        name: '资产分布',
        type: 'bar',
        data: pieChartData.value,
        itemStyle: {
          color: params => {
            return (params.value as number) >= 0 ? '#c23531' : '#61a0a8';
          },
        },
      },
    ],
    xAxis: {
      type: 'category',
      data: accounts.value.map(account => account.accountName),
      axisLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.6)',
        },
      },
      axisLabel: {
        color: '#fff',
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        formatter: val => {
          return formatNumber(val, { separator: true, abbreviate: true, fix: 0 });
        },
      },
    },
    grid: {
      containLabel: true,
      bottom: 20,
      left: 20,
      top: 20,
      right: 20,
    },
  };
});

// 监听产品变化，获取产品账户
watch(
  () => product?.id,
  async newProductId => {
    if (newProductId) {
      await fetchProductDetail(newProductId);
    }
  },
);

// 初始化
onMounted(async () => {
  if (product) {
    await fetchProductDetail(product.id);
  }
});

// 获取产品账户
const fetchProductDetail = async (id: number | string) => {
  productDetail.value = null;
};
</script>

<template>
  <div>
    <el-scrollbar>
      <div h-270 flex aic>
        <!-- 收益分布图 -->
        <div w-360 h-240 bg="[--g-panel-bg2]" mx-16 class="card">
          <VueEchart :option="pieChartOption" />
        </div>
        <!-- 账户卡片列表 -->
        <div flex>
          <div
            v-for="(account, index) in accounts"
            :key="index"
            w-300
            h-240
            bg="[--g-panel-bg2]"
            mr-16
            class="card"
          >
            <div px-20 py-12 fs-14 flex aic c-white toe>
              <div>{{ account.accountName }}</div>
              <div>（{{ account.accountId }}）</div>
            </div>
            <div>
              <!-- 总资产 -->
              <div px-20 fs-24 font-bold c-white>
                {{ formatNumber(account.balance, { separator: true }) }}
              </div>
              <div py-20 px-20 flex jcsb border-b="1px solid [--g-border]">
                <div>
                  <div>可用资产</div>
                  <div c-white>
                    {{ formatNumber(account.available, { separator: true }) }}
                  </div>
                </div>
                <div>
                  <div>持仓市值</div>
                  <div c-white>
                    {{ formatNumber(account.marketValue, { separator: true }) }}
                  </div>
                </div>
                <div>
                  <div>浮动盈亏</div>
                  <div :class="getColorClass(account.positionProfit)">
                    {{ formatNumber(account.positionProfit, { separator: true, prefix: true }) }}
                  </div>
                </div>
              </div>
              <!-- TODO: 不同账号应该有不同的展示内容 -->
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped>
.card {
  border-radius: 8px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.4);
}
</style>
