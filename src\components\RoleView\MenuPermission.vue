<script setup lang="tsx">
import { watch } from 'vue';
import type { RoleMenuPermissionTree, RoleMomPermission } from '@/types';
import type { MomMenuTree } from '../../../../xtrade-sdk/dist';

const { fullMenus, roleMenus, initStatusCount } = defineProps<{
  fullMenus: RoleMenuPermissionTree[];
  roleMenus: MomMenuTree[];
  initStatusCount: number;
}>();

const emit = defineEmits<{
  'update-parent': [];
}>();

const getChileRoleMenus = (menu: MomMenuTree) => {
  return roleMenus.find(item => item.id === menu.id)?.children || [];
};

const getMenuPermissionTypes = (menu: RoleMenuPermissionTree) => {
  const permissions = menu.menListPermission || [];
  // 将permissions按type分组
  const types: Record<string, RoleMomPermission[]> = {};
  permissions.forEach(permission => {
    if (!types[permission.type]) {
      types[permission.type] = [];
    }
    types[permission.type].push(permission);
  });
  return types;
};

// 初始化时根据roleMenus设置checked状态
const initializeCheckedStatus = () => {
  const setCheckedStatus = (menus: RoleMenuPermissionTree[], roleMenus: MomMenuTree[]) => {
    menus.forEach(menu => {
      const roleMenu = roleMenus.find(rm => rm.id === menu.id);

      // 重置菜单状态
      menu.checked = !!roleMenu;

      // 重置权限状态
      if (menu.menListPermission) {
        menu.menListPermission.forEach(permission => {
          const rolePermission = roleMenu?.menListPermission?.find(rp => rp.id === permission.id);
          permission.checked = !!rolePermission;
        });
      }

      // 递归处理子菜单
      if (menu.children) {
        setCheckedStatus(menu.children, roleMenu?.children || []);
      }
    });
  };

  setCheckedStatus(fullMenus, roleMenus);
};

// 监听roleMenus变化，重新初始化checked状态
watch(
  () => initStatusCount,
  () => {
    initializeCheckedStatus();
  },
  { immediate: true, deep: true },
);

// 检查菜单是否应该为中间状态
const getMenuIndeterminate = (menu: RoleMenuPermissionTree): boolean => {
  if (!menu.children && !menu.menListPermission) return false;

  let checkedCount = 0;
  let totalCount = 0;
  let hasIndeterminate = false;

  // 检查子菜单
  if (menu.children) {
    menu.children.forEach(child => {
      totalCount++;
      if (child.checked) checkedCount++;
      // 如果子菜单有中间状态，父级也应该是中间状态
      if (getMenuIndeterminate(child)) {
        hasIndeterminate = true;
      }
    });
  }

  // 检查权限
  if (menu.menListPermission) {
    menu.menListPermission.forEach(permission => {
      totalCount++;
      if (permission.checked) checkedCount++;
    });
  }

  return hasIndeterminate || (checkedCount > 0 && checkedCount < totalCount);
};

// 处理菜单checkbox变化
const handleMenuChange = (menu: RoleMenuPermissionTree, checked: boolean) => {
  menu.checked = checked;

  // 设置所有子菜单的状态
  if (menu.children) {
    menu.children.forEach(child => {
      handleMenuChange(child, checked);
    });
  }

  // 设置所有权限的状态
  if (menu.menListPermission) {
    menu.menListPermission.forEach(permission => {
      permission.checked = checked;
    });
  }

  // 更新父级菜单状态
  emit('update-parent');
};

// 处理权限checkbox变化
const handlePermissionChange = (permission: RoleMomPermission) => {
  // 找到该权限所属的菜单
  const parentMenu = findMenuByPermission(fullMenus, permission);
  if (parentMenu) {
    // 检查该菜单下的所有权限状态，决定菜单是否应该被选中
    updateMenuStatusByPermissions(parentMenu);
  }

  // 更新所有父级菜单状态
  emit('update-parent');
};

// 根据权限查找所属菜单
const findMenuByPermission = (
  menus: RoleMenuPermissionTree[],
  permission: RoleMomPermission,
): RoleMenuPermissionTree | null => {
  for (const menu of menus) {
    // 检查当前菜单的权限
    if (menu.menListPermission?.some(p => p.id === permission.id)) {
      return menu;
    }

    // 递归检查子菜单
    if (menu.children) {
      const found = findMenuByPermission(menu.children, permission);
      if (found) return found;
    }
  }
  return null;
};

// 根据权限状态更新菜单状态
const updateMenuStatusByPermissions = (menu: RoleMenuPermissionTree) => {
  if (!menu.menListPermission || menu.menListPermission.length === 0) {
    return;
  }

  const checkedPermissions = menu.menListPermission.filter(p => p.checked);
  const hasIndeterminate = getMenuIndeterminate(menu);

  // 如果有中间状态，菜单应该被选中
  // 如果所有权限被选中，菜单应该被选中
  // 如果没有权限被选中，菜单应该取消选中
  menu.checked = hasIndeterminate || checkedPermissions.length > 0;
};

// 更新父级菜单状态（递归向上更新所有父级）
const updateParentMenuStatus = () => {
  const updateMenuStatus = (menus: RoleMenuPermissionTree[]) => {
    menus.forEach(menu => {
      // 先递归更新子菜单
      if (menu.children) {
        updateMenuStatus(menu.children);

        // 检查子菜单和权限状态
        let hasCheckedChild = false;
        let hasUncheckedChild = false;
        let hasIndeterminate = false;

        // 检查子菜单状态
        menu.children.forEach(child => {
          if (child.checked) {
            hasCheckedChild = true;
          } else {
            hasUncheckedChild = true;
          }
          // 如果子菜单有中间状态，父级也应该是中间状态
          if (getMenuIndeterminate(child)) {
            hasIndeterminate = true;
          }
        });

        // 检查权限状态
        if (menu.menListPermission) {
          menu.menListPermission.forEach(permission => {
            if (permission.checked) {
              hasCheckedChild = true;
            } else {
              hasUncheckedChild = true;
            }
          });
        }

        // 设置菜单状态
        if (hasIndeterminate || (hasCheckedChild && hasUncheckedChild)) {
          // 有中间状态或部分子项被选中，菜单应该被选中但显示为中间状态
          menu.checked = true;
        } else if (hasCheckedChild && !hasUncheckedChild) {
          // 所有子项都被选中
          menu.checked = true;
        } else {
          // 所有子项都未被选中
          menu.checked = false;
        }
      }
    });
  };
  updateMenuStatus(fullMenus);
  emit('update-parent');
};
</script>
<template>
  <div flex-col flex gap-16 flex-1 min-w-1>
    <div v-for="menu in fullMenus" :key="menu.menuName" flex gap-16>
      <div h-31 flex aic gap-8>
        <el-checkbox
          :model-value="menu.checked"
          :indeterminate="getMenuIndeterminate(menu)"
          @change="checked => handleMenuChange(menu, !!checked)"
        />
        <div flex-1 min-w-1 cursor-pointer @click="handleMenuChange(menu, !menu.checked)">
          {{ menu.menuName }}
        </div>
      </div>
      <!-- 非叶子节点 -->
      <menu-permission
        v-if="menu.children"
        :full-menus="menu.children"
        :role-menus="getChileRoleMenus(menu)"
        :init-status-count="initStatusCount"
        @update-parent="updateParentMenuStatus"
      ></menu-permission>
      <!-- 叶子节点菜单 -->
      <div v-else flex flex-col gap-16 flex-1 min-w-1>
        <div v-for="(permissions, type) in getMenuPermissionTypes(menu)" :key="type" flex gap-16>
          <div h-31 flex aic gap-16>{{ type }}：</div>
          <div flex-1 min-w-1 flex flex-wrap gap-16>
            <div v-for="permission in permissions" :key="permission.id">
              <div h-31 flex aic gap-8>
                <el-checkbox
                  :model-value="permission.checked"
                  @change="
                    checked => {
                      permission.checked = !!checked;
                      handlePermissionChange(permission);
                    }
                  "
                />
                <div
                  cursor-pointer
                  @click="
                    permission.checked = !permission.checked;
                    handlePermissionChange(permission);
                  "
                >
                  {{ permission.permissionZhName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
