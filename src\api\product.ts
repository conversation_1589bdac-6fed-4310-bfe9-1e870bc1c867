import { Repos } from '../../../xtrade-sdk';

const govRepo = new Repos.GovernanceRepo();

class ProductService {
  static async getProducts() {
    try {
      const resp = await govRepo.QueryProducts();
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      return [];
    }
  }
}

export default ProductService;
