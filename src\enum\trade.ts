import { enumToArray } from '../script';
import { type TradeChannel } from '../types/trade';

export enum AssetTypeEnum {
  期货 = 1,
  股票 = 2,
  期权 = 3,
  债券 = 4,
  基金 = 5,
  现货 = 6,
  回购 = 7,
}

/** 交易渠道 */
export const TRADE_CHANNELS: TradeChannel[] = [
  {
    label: '现货竞价',
    name: 'spot',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
  },
  {
    label: '融资融券',
    name: 'credit',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
    credit: true,
  },
  // {
  //   label: '期货',
  //   name: 'future',
  //   assetTypes: [AssetTypeEnum.期货],
  // },
];

/** 交易方向 */
export enum TradeDirectionEnum {
  买入 = 1,
  卖出 = -1,
}

export const TRADE_DIRECTIONS = enumToArray(TradeDirectionEnum);

/** 业务标志 */
export enum BusinessFlagEnum {
  普通买卖 = 0,
  担保品买卖 = 1,
  融资融券 = 2,
  还款还券 = 3,
  现券还券 = 4,
  直接还款 = 5,
}

/** 价格类型 */
export enum OrderPriceTypeEnum {
  限价 = 1,
  市价 = 2,
  模拟 = 3,
}

/** 对冲标志 */
export enum HedgeFlagEnum {
  Start = 0,
  投机 = 1,
  套利 = 2,
  强平 = 3,
  End = 4,
}
