/**
 * 账户信息接口定义
 */
export interface LegacyAccountInfo {
  /** 账户ID */
  accountId: string;
  /** 账户名称 */
  accountName: string;
  /** 资产类型，数值表示不同类型 */
  assetType: number;
  /** 是否自动审批 */
  autoApprove: boolean;
  /** 可用金额 */
  available: number;
  /** 是否进行可用金额检查 */
  availableCheck: boolean;
  /** 总权益 */
  balance: number;
  /** 银行ID */
  bkId: number;
  /** 券商ID */
  brokerId: string;
  /** 券商名称 */
  brokerName: string;
  /** 是否为子账户 */
  childAccount: boolean;
  /** 平仓利润 */
  closeProfit: number;
  /** 佣金 */
  commission: number;
  /** 连接状态 */
  connectionStatus: boolean;
  /** 是否为信用账户 */
  credit: boolean;
  /** 当日利润 */
  dayProfit: number;
  /** 详情ID */
  detailId: string;
  /** 余额差异 */
  diffBalance: number;
  /** 扩展信息，包含内嵌字段，在上下行数据传输时需转换（数据库存储为字符串格式） */
  extInfo: AccountExtInfo;
  /** 资金账号密码（仅从客户端提交到服务器，服务器端不返回该字段） */
  pwd: string | null;
  /** 资金账户 */
  financeAccount: string;
  /** 冻结佣金 */
  frozenCommission: number;
  /** 冻结保证金 */
  frozenMargin: number;
  /** 反向映射当前的账号所绑定到的产品 */
  funds: AccountBelongFund[];
  /** 唯一标识ID */
  id: string;
  /** 身份ID */
  identityId: string;
  /** 转入金额 */
  inMoney: number;
  /** 增加金额 */
  increaseAmount: number;
  /** 初始化状态 */
  initStatus: boolean;
  /** 接口类型 */
  interfaceType: number;
  /** 融资买入余额 */
  loanBuyBalance: number;
  /** 融券卖出余额 */
  loanSellBalance: number;
  /** 融券卖出额度 */
  loanSellQuota: number;
  /** 保证金 */
  margin: number;
  /** 市值 */
  marketValue: number;
  /** 最大限额资金 */
  maxLimitMoney: number;
  /** 净值 */
  nav: number;
  /** 机构ID */
  orgId: number;
  /** 机构名称 */
  orgName: string;
  /** 转出金额 */
  outMoney: number;
  /** 持仓利润 */
  positionProfit: number;
  /** 昨日余额 */
  preBalance: number;
  /** 昨日净值 */
  preNav: number;
  /** 昨日风控状态 */
  preRiskControl: boolean;
  /** 利润率 */
  profitRatio: number;
  /** 涨幅百分比 */
  risePercent: number;
  /** 账户状态 */
  status: number;
  /** 总利润 */
  sumProfit: number;
  /** 终端列表 */
  terminals: AccountTerminalInfo[];
  /** 工作流ID */
  workFlowId: number;
  /** 工作流名称 */
  workFlowName: string;
}

/**
 * 账号扩展信息
 */
export interface AccountExtInfo {
  /** 交易账号 */
  tradeAccount: string;
  /** 通信密码 */
  txPassword: string;
  /** 沪市股东代码 */
  shSecId: string;
  /** 深市股东代码 */
  szSecId: string;
  /** 营业部代码 */
  yybId: string;
  /** 版本号 */
  version: string;
}

/**
 * 账号所绑定到的产品
 */
export interface AccountBelongFund {
  /** 资金ID */
  fundId: string;
  /** 资金名称 */
  fundName: string;
}

/**
 * 账号交易终端
 */
export interface AccountTerminalInfo {
  /** 终端ID */
  id: number;
  /** 接口类型 */
  interfaceType: number;
  /** 终端状态 */
  status: number;
  /** 终端名称 */
  terminalName: string;
}

/**
 * 资金账户明细表
 */
export interface MomFinanceAccountDetail {
  /** 帐号ID（虚拟账号ID或帐号ID） */
  id: number;

  /** 帐号类型（帐号/虚拟账号/产品） */
  idType: number;

  /** 交易日 */
  tradingDay: number;

  /** 权益 */
  balance: number;

  /** 可用资金 */
  available: number;

  /** 可取资金 */
  withdrawBalance: number;

  /** 平仓盈亏 */
  closeProfit: number;

  /** 持仓盈亏 */
  positionProfit: number;

  /** 昨日权益 */
  preBalance: number;

  /** 保证金（期货：当前保证金；融资融券：保证金占用） */
  margin: number;

  /** 维持担保比例（无符号，补零到4位小数） */
  marginRatio: number;

  /** 净资产（无符号，补零到4位小数） */
  netAssets: number;

  /** 授信总额度（无符号，补零到4位小数） */
  finMaxQuota: number;

  /** 融资额度（无符号，补零到4位小数） */
  finAmount: number;

  /** 融券额度（无符号，补零到4位小数） */
  securityAmount: number;

  /** 融资占用额度（无符号，补零到4位小数） */
  finUsedAmount: number;

  /** 融券占用额度（无符号，补零到4位小数） */
  securityUsedAmount: number;

  /** 融资融券总负债（无符号，补零到2位小数） */
  totalDebt: number;

  /** 入金金额 */
  inMoney: number;

  /** 出金金额 */
  outMoney: number;

  /** 冻结的保证金（期货：保证金；股票：下单冻结资金） */
  frozenMargin: number;

  /** 冻结的手续费 */
  frozenCommission: number;

  /** 手续费 */
  commission: number;

  /** 市值 */
  marketValue: number;

  /** 当日盈亏 */
  dayProfit: number;

  /** 累计盈亏（整数） */
  sumProfit: number;

  /** 净值（3位小数） */
  nav: number;

  /** 上个交易日净值（3位小数） */
  preNav: number;

  /** 份额 */
  fundShare: number;

  /** 今日收益率（4位小数） */
  profitRatio: number;

  /** 创建时间 */
  createTime: number;

  /** 更新时间 */
  updateTime: number;
}

/**
 * 账户权益信息
 */
export interface AccountEquityInfo {
  /** 账户ID */
  account_id: number | string;
  /** 昨日余额 */
  pre_balance: number;
  /** 可用资金 */
  available: number;
  /** 冻结资金 */
  frozen: number;
  /** 买入金额 */
  buy_balance: number;
  /** 卖出金额 */
  sell_balance: number;
  /** 卖出额度 */
  sell_quota: number;
}

/**
 * 费用信息接口，用于定义账户相关的各项费用和基础信息。
 */
export interface AccountFeeInfo {
  /**
   * 唯一标识符，表示该费用记录的 ID。
   */
  id: string | number;

  /**
   * 账户 ID，关联该费用信息的账户唯一标识。
   */
  accountId: string | number;

  /**
   * 账户名称，便于识别的账户显示名称。
   */
  accountName: string;

  /**
   * 佣金，交易中需支付的佣金费用。
   */
  commission: number;

  /**
   * 最低佣金，佣金收取的最低金额限制。
   */
  commissionLeast: number;

  /**
   * 上海市场其他费用，指在上海证券交易所交易产生的其他杂费。
   */
  shOther: number;

  /**
   * 上海市场过户费，股票过户时在上海市场产生的费用。
   */
  shTransferFee: number;

  /**
   * 印花税，卖出股票时需缴纳的税费。
   */
  stampDuty: number;

  /**
   * 深圳市场其他费用，指在深圳证券交易所交易产生的其他杂费。
   */
  szOther: number;

  /**
   * 深圳市场过户费，股票过户时在深圳市场产生的费用。
   */
  szTransferFee: number;

  /**
   * 记录创建时间，通常为时间戳或 ISO 格式字符串。
   */
  createTime: string | number;

  /**
   * 记录最后更新时间，通常为时间戳或 ISO 格式字符串。
   */
  updateTime: string | number;
}

/**
 * 账户费率信息
 */
export interface AccountCommissionInfo {}

/**
 * 创建一个空的账户记录
 */
export function CreateEmptyAccountRecord(): LegacyAccountInfo {
  return {
    accountId: null as any,
    accountName: '',
    assetType: 2,
    autoApprove: false,
    available: 0,
    availableCheck: false,
    balance: 0,
    bkId: null as any,
    brokerId: null as any,
    brokerName: '',
    childAccount: false,
    closeProfit: 0,
    commission: 0,
    connectionStatus: false,
    credit: false,
    dayProfit: 0,
    detailId: null as any,
    diffBalance: 0,
    extInfo: {
      tradeAccount: null as any,
      txPassword: null as any,
      shSecId: null as any,
      szSecId: null as any,
      yybId: null as any,
      version: null as any,
    },
    pwd: null,
    financeAccount: '',
    frozenCommission: 0,
    frozenMargin: 0,
    funds: [],
    id: null as any,
    identityId: null as any,
    inMoney: 0,
    increaseAmount: 0,
    initStatus: false,
    interfaceType: 0,
    loanBuyBalance: 0,
    loanSellBalance: 0,
    loanSellQuota: 0,
    margin: 0,
    marketValue: 0,
    maxLimitMoney: 0,
    nav: 0,
    orgId: null as any,
    orgName: '',
    outMoney: 0,
    positionProfit: 0,
    preBalance: 0,
    preNav: 0,
    preRiskControl: false,
    profitRatio: 0,
    risePercent: 0,
    status: 0,
    sumProfit: 0,
    terminals: [],
    workFlowId: null as any,
    workFlowName: '',
  };
}
