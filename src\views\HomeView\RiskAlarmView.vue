<script setup lang="ts">
import { shallowRef } from 'vue';
import { deepClone } from '@/script';
import type { MomTerminal } from '../../../../xtrade-sdk/dist';

// 当前选中行
const activeRow = shallowRef<MomTerminal>();

// 选择事件
const handleRowSelect = (rowd: MomTerminal) => {
  activeRow.value = rowd;
  console.log('表格行选择：', deepClone(rowd));
};
</script>

<template>
  <div flex>预警消息</div>
</template>

<style scoped></style>
