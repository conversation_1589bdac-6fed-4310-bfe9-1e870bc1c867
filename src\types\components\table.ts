import type { Column } from 'element-plus';
import type { JSX } from 'vue/jsx-runtime';
/** 表格列定义 */
export type ColumnDefinition<T> = {
  [K in keyof T]: Column<T[K]> & {
    key: K;
    dataKey?: K;
    cellRenderer?: (props: {
      cellData: T[K];
      column: Column<T[K]>;
      rowData: T;
      rowIndex: number;
      columnIndex: number;
      columns: Column<T[K]>[];
    }) => JSX.Element | string | number;
  };
}[keyof T][];

/** 行操作 */
export interface RowAction<T> {
  /** 按钮文字（文字与图标2选1） */
  label?: string;
  /** 按钮图标（文字与图标2选1） */
  icon?: string;
  /** 按钮类型（颜色） */
  type?: 'text' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'default' | any;
  /** 点击事件 */
  onClick: (rowData: T) => void;
  /** 是否显示 */
  show?: (rowData: T) => boolean;
  /** 是否禁用 */
  disabled?: (rowData: T) => boolean;
  /** 内嵌操作按钮 */
  nesteds?: RowAction<T>[];
}
