<script setup lang="ts">
import { computed } from 'vue';
import { formatNumber, getColorClass } from '@/script';
import type { StandardTick } from '@/types';

const { lastTick } = defineProps<{
  lastTick?: StandardTick;
}>();

// 定义emit事件
const emit = defineEmits<{
  clickLevel: [price: number];
}>();

// 档位
const priceLevels = computed(() => {
  return [
    { level: 5, price: lastTick?.askPrice[4], volume: lastTick?.askVolume[4] },
    { level: 4, price: lastTick?.askPrice[3], volume: lastTick?.askVolume[3] },
    { level: 3, price: lastTick?.askPrice[2], volume: lastTick?.askVolume[2] },
    { level: 2, price: lastTick?.askPrice[1], volume: lastTick?.askVolume[1] },
    { level: 1, price: lastTick?.askPrice[0], volume: lastTick?.askVolume[0] },
    { level: -1, price: lastTick?.bidPrice[0], volume: lastTick?.bidVolume[0] },
    { level: -2, price: lastTick?.bidPrice[1], volume: lastTick?.bidVolume[1] },
    { level: -3, price: lastTick?.bidPrice[2], volume: lastTick?.bidVolume[2] },
    { level: -4, price: lastTick?.bidPrice[3], volume: lastTick?.bidVolume[3] },
    { level: -5, price: lastTick?.bidPrice[4], volume: lastTick?.bidVolume[4] },
  ];
});

// 底部信息字段定义
const tradeInfoFields = computed(() => {
  const percent = !lastTick
    ? ''
    : (lastTick.lastPrice - lastTick.preClosePrice) / lastTick.preClosePrice;

  return [
    {
      label: '昨收',
      key: 'yesterdayPrice',
      value: formatNumber(lastTick?.preClosePrice, {
        default: '--',
      }),
      needColor: false,
    },
    {
      label: '今开',
      key: 'todayPrice',
      value: formatNumber(lastTick?.openPrice, {
        default: '--',
      }),
      needColor: true,
      colorValue: !lastTick ? '' : lastTick?.openPrice - lastTick?.preClosePrice,
    },
    {
      label: '最新',
      key: 'latestPrice',
      value: formatNumber(lastTick?.lastPrice, {
        default: '--',
      }),
      needColor: true,
      colorValue: !lastTick ? '' : lastTick?.lastPrice - lastTick?.preClosePrice,
    },
    {
      label: '涨跌',
      key: 'changePercent',
      value: formatNumber(percent, { percent: true, default: '--' }),
      needColor: true,
      colorValue: percent,
      isFormatted: true,
    },
    // {
    //   label: '均价',
    //   key: 'avgPrice',
    //   value: tradeInfo.value.avgPrice,
    //   needColor: false,
    // },
  ];
});

// 点击价格档位修改价格输入框中的值
const handleClickLevel = (level: (typeof priceLevels)['value'][0]) => {
  if (level.price) {
    emit('clickLevel', level.price);
  }
};
</script>

<template>
  <div p="10" h-full>
    <!-- 价格档位列表 -->
    <div>
      <div
        v-for="level in priceLevels"
        :key="level.level"
        flex
        aic
        jcsb
        h-30
        pl-10
        pr-10
        cursor-pointer
        transition="all duration-100"
        hover="bg-[--g-bg-hover-2]"
        @click="handleClickLevel(level)"
      >
        <!-- 档位标签 -->
        <div w="33%" flex aic>
          {{ level.level > 0 ? '卖' + level.level : '买' + Math.abs(level.level) }}
        </div>

        <!-- 价格 -->
        <div w="33%" flex aic jcc :class="getColorClass(level.price! - lastTick?.preClosePrice!)">
          {{ formatNumber(level.price, { default: '--' }) }}
        </div>

        <!-- 数量 -->
        <div flex-1 flex jce>
          {{ formatNumber(level.volume ? level.volume / 100 : 0, { default: '--', fix: 0 }) }}
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div b-t="1px solid [--g-border]" p="10" mt-10 flex="~ wrap" gap-10>
      <div w="45%" v-for="field in tradeInfoFields" :key="field.key" flex aic jcsb>
        <div w-40>{{ field.label }}</div>
        <div
          :class="
            field.needColor && field.colorValue !== undefined ? getColorClass(field.colorValue) : ''
          "
        >
          {{ field.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
