<script setup lang="ts">
import { computed, reactive, watch, shallowRef } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { MomPermission, FormPermission } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';

interface Props {
  visible: boolean;
  permission?: MomPermission;
  menuId?: number;
  permissions?: MomPermission[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'saved'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单引用
const formRef = shallowRef<FormInstance>();

// 表单数据
const formData = reactive<FormPermission>({
  permissionName: '',
  permissionZhName: '',
  functionCode: 0,
  method: undefined,
  url: undefined,
  menuId: 0,
  type: '',
});

// 表单验证规则
const rules: FormRules = {
  permissionName: [
    { required: true, message: '请输入权限标识', trigger: 'blur' },
    { min: 1, max: 100, message: '权限标识长度在 1 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
      message: '权限标识只能包含字母、数字和下划线，且以字母或下划线开头',
      trigger: 'blur',
    },
  ],
  permissionZhName: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 1, max: 50, message: '权限名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  functionCode: [
    { required: true, message: '请输入功能码', trigger: 'blur' },
    { type: 'number', min: 0, message: '功能码必须大于等于0', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请输入权限分类', trigger: 'change' }],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
});

const isEdit = computed(() => !!props.permission);

const dialogTitle = computed(() => (isEdit.value ? '编辑权限' : '创建权限'));

// 请求方式选项
const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'TCP', value: 'TCP' },
];

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      resetForm();
    }
  },
);

// 重置表单
const resetForm = () => {
  if (props.permission) {
    // 编辑模式
    Object.assign(formData, {
      permissionName: props.permission.permissionName,
      permissionZhName: props.permission.permissionZhName,
      functionCode: props.permission.functionCode,
      method: props.permission.method,
      url: props.permission.url,
      menuId: props.permission.menuId,
      type: props.permission.type,
    });
  } else {
    // 创建模式
    Object.assign(formData, {
      permissionName: '',
      permissionZhName: '',
      functionCode: 0,
      method: undefined,
      url: undefined,
      menuId: props.menuId || 0,
      type: '',
    });
  }

  // 清除验证状态
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(async valid => {
    if (valid) {
      // 检查权限标识唯一性
      const isDuplicate = props.permissions?.some(p => {
        if (isEdit.value && props.permission) {
          // 编辑模式：排除当前正在编辑的权限
          return p.id !== props.permission.id && p.permissionName === formData.permissionName;
        }
        // 创建模式：检查所有权限
        return p.permissionName === formData.permissionName;
      });

      if (isDuplicate) {
        ElMessage.error('权限标识已存在，请更换后重试');
        return;
      }

      if (isEdit.value && props.permission) {
        // 编辑权限
        const updateData: MomPermission = {
          ...props.permission,
          ...formData,
        };
        const { errorCode, errorMsg } = await AdminService.updatePermission(updateData);
        if (errorCode !== 0) {
          ElMessage.error(errorMsg || '权限更新失败');
          return;
        }
        ElMessage.success('权限更新成功');
      } else {
        // 创建权限
        const { errorCode, errorMsg } = await AdminService.createPermission(formData);
        if (errorCode !== 0) {
          ElMessage.error(errorMsg || '权限创建失败');
          return;
        }
        ElMessage.success('权限创建成功');
      }

      emit('saved');
    }
  });
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="400px"
    :close-on-click-modal="false"
    draggable
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="权限分类" prop="type">
        <el-input v-model="formData.type" placeholder="请输入权限分类" />
      </el-form-item>

      <el-form-item label="权限标识" prop="permissionName">
        <el-input
          v-model="formData.permissionName"
          placeholder="请输入权限标识，如：create、edit、delete"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="权限名称" prop="permissionZhName">
        <el-input
          v-model="formData.permissionZhName"
          placeholder="请输入权限中文名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="功能码" prop="functionCode">
        <el-input-number
          v-model="formData.functionCode"
          :min="0"
          :max="999999"
          placeholder="请输入功能码"
          w-full
          :controls="false"
        />
      </el-form-item>

      <el-form-item label="请求方式">
        <el-select v-model="formData.method" placeholder="请选择请求方式（可选）" clearable w-full>
          <el-option
            v-for="option in methodOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <!-- TODO: 修改为下拉选择地址 -->
      <el-form-item label="URL地址">
        <el-input v-model="formData.url" placeholder="请输入URL地址（可选）" maxlength="200" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-input-number) {
  width: 100%;
}
:deep() {
  .el-input__inner {
    text-align: left;
  }
}
</style>
