export const deleteConfirm = async (title: string, message: string): Promise<boolean> => {
  return new Promise(resolve => {
    import('element-plus').then(({ ElMessageBox }) => {
      ElMessageBox({
        title,
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete',
        type: 'error',
        showClose: false,
        message,
        buttonSize: 'large',
        icon: () => <i class="fs-24 iconfont icon-warning-circle-fill" />,
      })
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  });
};
