import Utils from '../modules/utils';
import { BaseRepo } from '../modules/base-repo';
import { LegacyFundInfo, LegacyFundBasic, LegacyFundDetail } from '../types/table/fund';
import { AccountEquityInfo, AccountFeeInfo, LegacyAccountInfo } from '../types/table/account';
import { HttpResponseData } from '../types/common';

export class GovernanceRepo extends BaseRepo {
    
    /**
     * 查询权限范围内能看到的所有产品
     */
    async QueryProducts(): Promise<HttpResponseData<LegacyFundInfo[]>> {

        const resp1 = await this.assist.Get<LegacyFundBasic[]>('/fund');
        const ids = (resp1.data || []).map(x => x.id);
        const resp2 = await this.assist.Post<LegacyFundDetail[]>('/fund/detail', {}, ids);

        if (resp1.errorCode == 0 && resp2.errorCode == 0) {

            const mergeds: LegacyFundInfo[] = [];
            const basics = resp1.data || [];
            const details = resp2.data || [];
            
            basics.forEach(basic => {

                const matched = details.find(detail => detail.fundId == basic.id);
                if (matched) {
                    mergeds.push(Object.assign(basic, matched));
                }
                else {
                    mergeds.push(basic as any);
                }
            });

            resp1.data = mergeds;
        }
        
        const { errorCode, errorMsg, data } = resp1;
        return { errorCode, errorMsg, data: data as LegacyFundInfo[] };
    }

    /**
     * 创建产品
     */
    async CreateProduct(data: LegacyFundInfo) {
        return await this.assist.Post<LegacyFundInfo>('/fund', {}, data);
    }

    /**
     * 更改产品
     */
    async UpdateProduct(data: LegacyFundInfo) {
        return await this.assist.Put<LegacyFundInfo>('/fund', {}, data);
    }

    /**
     * 删除产品
     */
    async DeleteProduct(fund_id: string) {
        return await this.assist.Delete<[]>('/fund', { fund_id });
    }

    /**
     * 查询权限范围内能看到的所有策略（详情）
     */
    async QueryStrategies() {
        throw new Error('not implemented');
    }

    /**
     * 查询权限范围内能看到的所有账号（较简）
     */
    async QueryAccounts() {

        const resp = await this.assist.Get<LegacyAccountInfo[]>('/account');
        const { errorCode, errorMsg, data } = resp;
        
        if (errorCode === 0 && Array.isArray(data) && data.length >= 1) {
            
            const uniques = Utils.unique(data, x => x.id);
            uniques.forEach(item => {
                try {
                    const parsed = JSON.parse(item.extInfo as any as string);
                    item.extInfo = Object.assign({ txPassword: '', version: '' }, parsed);
                }
                catch(ex) {
                    item.extInfo = { tradeAccount: '', txPassword: '', shSecId: '', szSecId: '', yybId: '', version: '' };
                }
            });

            /**
             * 补齐资金账号密码（服务器端不返回该字段，仅单向上行修改）
             */

            uniques.forEach(item => {
                item.pwd = null;
            });

            resp.data = uniques;
        }
        
        return resp;
    }

    /**
     * 创建账号
     */
    async CreateAccount(account: LegacyAccountInfo) {

        const cloned = Utils.deepClone(account);
        if (Utils.isJson(cloned.extInfo)) {
            cloned.extInfo = JSON.stringify(cloned.extInfo) as any;
        }
        return await this.assist.Post<LegacyAccountInfo>('/account', {}, cloned);
    }

    /**
     * 更新账号
     */
    async UpdateAccount(account: LegacyAccountInfo) {

        const cloned = Utils.deepClone(account);
        if (Utils.isJson(cloned.extInfo)) {
            cloned.extInfo = JSON.stringify(cloned.extInfo) as any;
        }
        return await this.assist.Put<LegacyAccountInfo>('/account', {}, cloned);
    }

    /**
     * 删除账号
     */
    async DeleteAccount(account_id: string) {
        return await this.assist.Delete<[]>('/account', { account_id });
    }

    /**
     * 连接账号（连接成功返回结果，为一个空数组）
     */
    async ConnectAccount(account_id: string) {
        return await this.assist.Get<[]>('/account/connection_test', { account_id });
    }

    /**
     * 断开一个已连接的账号（连接成功返回结果，为一个空数组）
     */
    async DisconnectAccount(account_id: string) {
        return await this.assist.Put<[]>('/account/logout', { account_id }, {});
    }

    /**
     * 设置账号权益
     */
    async SetAccountEquity(data: AccountEquityInfo) {
        return await this.assist.Post<[]>('/account/prebalance', {}, data);
    }

    /**
     * 查询账号已有费用设置
     */
    async QueryAccountFee(account_id: string) {
        return await this.assist.Get<AccountFeeInfo>('/account/fee', { account_id });
    }

    /**
     * 创建账号费用设置
     */
    async CreateAccountFee(data: AccountFeeInfo) {
        return await this.assist.Post<AccountFeeInfo>('/account/fee', {}, data);
    }

    /**
     * 修改账号费用设置
     */
    async UpdateAccountFee(data: AccountFeeInfo) {
        return await this.assist.Put<AccountFeeInfo>('/account/fee', {}, data);
    }

    /**
     * 账号初始化
     */
    async InitAccount(account_id: string) {
        return await this.assist.Post<[]>('/account/init', { account_id });
    }

    /**
     * 账号清算
     */
    async SettleAccount(account_id: string) {
        return await this.assist.Post<[]>('/account/settle', { account_id });
    }

    /**
     * 账号比对
     */
    async CompareAccount(account_id: string) {
        return await this.assist.Post<[]>('/account/overlap', { account_id, overlap: false });
    }

    /**
     * 账号覆盖
     */
    async OverwriteAccount(account_id: string) {
        return await this.assist.Post<[]>('/account/overlap', { account_id, overlap: true });
    }

    /**
     * 账号资金覆盖
     */
    async OverwriteAccountFinance(account_id: string) {
        return await this.assist.Post<[]>('/account/overlap/detail', { account_id, overlap: true });
    }

    /**
     * 为账号绑定交易终端
     */
    async BindAccountTerminals(account_id: string, terminalIds: number[]) {
        return await this.assist.Post<[]>('/account/bind-terminal', { account_id }, terminalIds);
    }
}