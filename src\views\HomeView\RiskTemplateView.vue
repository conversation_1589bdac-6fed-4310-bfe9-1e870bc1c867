<script setup lang="ts">
import TemplateList from '@/components/RiskTemplateView/TemplateList.vue';
import IndicatorTree from '@/components/RiskTemplateView/IndicatorTree.vue';
import IndicatorList from '@/components/RiskTemplateView/IndicatorList.vue';
import CommonSharedIndicator from '@/components/RiskTemplateView/IndicatorConfig/CommonSharedIndicator.vue';
import ApplyAssetScope from '@/components/RiskTemplateView/IndicatorConfig/ApplyAssetScope.vue';
import type { RiskIndicator, RiskRule, RiskTemplate } from '../../../../xtrade-sdk/dist';
import { reactive, ref, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Utils } from '@/script';

const isIndicatorCollapsed = ref(false);
const $tree = useTemplateRef('$tree');
const $tmpl = useTemplateRef('$tmpl');
const states = reactive({
  template: null as RiskTemplate | null,
  indicator: null as RiskIndicator | null,
});

function handleTemplateChange(tmpl: RiskTemplate | null) {
  states.template = tmpl ? Utils.deepClone(tmpl) : null;
}

function handleNodeSelect(idc: RiskIndicator) {
  states.indicator = Utils.deepClone(idc);
}

function handleAddIdc(callback: (tmpl: RiskTemplate, current: RiskIndicator) => void) {
  const tmpl = $tmpl.value!.getCurrent();
  if (!tmpl) {
    return ElMessage.error('请选择风控模板');
  }

  const current = $tree.value!.getCurrent();
  if (!current) {
    return ElMessage.error('请选择要添加的指标');
  }

  callback(tmpl, current);
}

function handleSelectRule(rule: RiskRule) {
  console.log('rule selected', rule);
}

function handleToggle() {
  isIndicatorCollapsed.value = !isIndicatorCollapsed.value;
}
</script>

<template>
  <div class="risk-template-view" flex>
    <div w-242 class="block">
      <TemplateList ref="$tmpl" @select="handleTemplateChange" @toggle="handleToggle" />
    </div>
    <div w-248 class="block" v-show="!isIndicatorCollapsed">
      <IndicatorTree ref="$tree" @select="handleNodeSelect" />
    </div>
    <div w-100 flex-1 flex flex-col>
      <div flex-1 min-h-100 jcc aic>
        <IndicatorList
          v-bind:context-template="states.template"
          @add="handleAddIdc"
          @select="handleSelectRule"
        ></IndicatorList>
      </div>
      <div h-400 flex>
        <div h-full w-540 overflow-x-auto>
          <CommonSharedIndicator></CommonSharedIndicator>
        </div>
        <div h-full flex-1>
          <ApplyAssetScope></ApplyAssetScope>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.block {
  background-color: var(--g-block-bg-2);
}
</style>
