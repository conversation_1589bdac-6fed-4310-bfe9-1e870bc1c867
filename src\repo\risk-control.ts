import { BaseRepo } from '../modules/base-repo';
import { RiskTemplate } from '../types/table/risk-control';

export class RiskControlRepo extends BaseRepo {
    /**
     * 查询所有风控模板
     */
    async QueryTemplates() {
        return await this.assist.Get<RiskTemplate[]>('/risk-control/template');
    }

    /**
     * 创建风控模板
     */
    async CreateTemplate(template: RiskTemplate) {
        return await this.assist.Post<RiskTemplate>('/risk-control/template', {}, template);
    }

    /**
     * 更新风控模板
     */
    async UpdateTemplate(template: RiskTemplate) {
        return await this.assist.Put<[]>('/risk-control/template', {}, template);
    }

    /**
     * 删除风控模板
     */
    async DeleteTemplate(template_id: number) {
        return await this.assist.Delete<[]>('/risk-control/template', { template_id });
    }
}
