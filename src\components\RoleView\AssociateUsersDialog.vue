<script setup lang="tsx">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import type { ColumnDefinition } from '@/types';
import { Repos, type MomRole, type MomUser } from '../../../../xtrade-sdk/dist';

const { role } = defineProps<{
  role?: MomRole;
}>();

const visible = defineModel<boolean>();
const repoInstance = new Repos.AdminRepo();

// 当前角色下的用户列表
const roleUsers = shallowRef<MomUser[]>([]);
// 全量用户列表
const allUsers = shallowRef<MomUser[]>([]);
// 搜索输入框的值
const searchValue = shallowRef('');
const autocompleteRef = useTemplateRef('autocompleteRef');

// 表格列定义
const columns: ColumnDefinition<MomUser> = [
  { key: 'username', title: '用户名', width: 120, sortable: true },
  { key: 'fullName', title: '姓名', width: 120, sortable: true },
  { key: 'phoneNo', title: '电话', width: 130, sortable: true },
  { key: 'email', title: '邮箱', width: 200, sortable: true },
];

// 过滤出不是当前角色的用户，用于自动补全
const availableUsers = computed(() => {
  if (!role) return [];
  return allUsers.value.filter(user => user.roleId !== role.id);
});

// 自动补全的查询函数
const querySearch = (queryString: string, cb: (suggestions: any[]) => void) => {
  const results = queryString
    ? availableUsers.value.filter(
        user =>
          user.username.toLowerCase().includes(queryString.toLowerCase()) ||
          user.fullName.toLowerCase().includes(queryString.toLowerCase()),
      )
    : availableUsers.value;

  cb(
    results.map(user => ({
      value: user.username,
      user: user,
      label: `${user.username} (${user.fullName})`,
      currentRole: user.roleName,
    })),
  );
};

// 选择用户时的处理
const handleSelect = (item: any) => {
  const user = item.user as MomUser;
  const newRoleName = role?.roleName || '';

  ElMessageBox.confirm(
    `确定将用户：${user.fullName}（${user.roleName}）角色修改为${newRoleName}？`,
    '确认修改角色',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      if (!role) return;

      // 调用修改用户接口
      const { errorCode, errorMsg } = await repoInstance.UpdateUser({
        ...user,
        roleId: role.id,
        roleName: role.roleName,
        userType: role.userType,
      });

      if (errorCode === 0) {
        ElMessage.success('角色修改成功');
        searchValue.value = '';
        // 重新加载数据
        await loadData();
      } else {
        ElMessage.error(errorMsg || '修改失败');
      }
    })
    .catch(() => {
      // 用户取消，清空输入框
      searchValue.value = '';
    });
};

// 加载数据
const loadData = async () => {
  if (!role) return;

  try {
    // TODO:加载当前角色下的用户
    const roleUsersResult = await repoInstance.QueryUsersByRole(role.id);
    if (roleUsersResult.errorCode === 0) {
      roleUsers.value = roleUsersResult.data || [];
    }

    // 加载全量用户
    const allUsersResult = await repoInstance.QueryUsers();
    if (allUsersResult.errorCode === 0) {
      allUsers.value = allUsersResult.data || [];
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  }
};

// 监听对话框显示状态
watch(visible, val => {
  if (val && role) {
    loadData();
  }
});

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  searchValue.value = '';
  roleUsers.value = [];
  allUsers.value = [];
};

onMounted(() => {
  if (visible.value && role) {
    loadData();
  }
});
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="`关联用户 - ${role?.roleName || ''}`"
    width="600px"
    @close="handleClose"
    draggable
    destroy-on-close
  >
    <div>
      <!-- 搜索框 -->
      <div w-200 mb-15>
        <el-autocomplete
          ref="autocompleteRef"
          v-model="searchValue"
          :fetch-suggestions="querySearch"
          placeholder="选择需要关联的用户"
          style="width: 100%"
          @select="handleSelect"
        >
          <template #default="{ item }">
            <div flex aic jcsb>
              <div>
                <span font-bold>{{ item.user.username }}</span>
                <span ml-10>{{ item.user.fullName }}</span>
              </div>
              <div>{{ item.currentRole }}</div>
            </div>
          </template>
        </el-autocomplete>
      </div>

      <!-- 当前角色用户列表 -->
      <div h-400>
        <VirtualizedTable :show-toolbar="false" :columns="columns" :data="roleUsers" />
      </div>
    </div>
  </el-dialog>
</template>

<style scoped></style>
