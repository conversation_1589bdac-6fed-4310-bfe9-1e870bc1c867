<script setup lang="ts">
import { remove } from '@/script';
import type { MetaDataItem } from '@/types';
import { Search } from '@element-plus/icons-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { showFileUploadDialog } from '@/services/file-upload-dialog.service';
import { Repos } from '../../../../../xtrade-sdk/dist';

const assetCategories: MetaDataItem[] = [
  { label: '股票-市场类', value: 1 },
  { label: '股票-风险警示类', value: 2 },
  { label: '债券', value: 3 },
  { label: '指数', value: 4 },
  { label: '期货', value: 5 },
];

const repoInstance = new Repos.RiskControlRepo();
const searchKeyword = ref('');
const category = ref<number>(assetCategories[0].value as number);
const assets = ref<MetaDataItem[]>([]);
const filteredAssets = computed(() => {
  const kw = (searchKeyword.value || '').toLowerCase();
  return assets.value.filter(item => (item.label || '').toLowerCase().includes(kw));
});

const choicesMap = reactive<{ [key: number]: number[] }>({});
assetCategories.forEach(item => {
  choicesMap[item.value as number] = [];
});

const choices = computed(getCurrentChoices);

function getCurrentChoices() {
  const matched = choicesMap[category.value as number];
  return matched || [];
}

function handleChange(id: any, checked: any) {
  const list = choices.value;
  if (checked && !list.includes(id)) {
    list.push(id);
  } else if (!checked && list.includes(id)) {
    remove(list, x => x == id);
  }
}

function makeLabel(category_id: any, category_name: string) {
  const count = choicesMap[category_id].length;
  return `${category_name} (${count > 0 ? count : '待选'})`;
}

// function reset() {
//   if (typeof category.value == 'number') {
//     choicesMap[category.value as number] = [];
//   }
// }

function show() {
  showFileUploadDialog({});
}

async function request() {
  // assets.value = await repoInstance.getAssetsByCategory(category.value);
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="view-apply-asset-setting" p-10 h-full>
    <div class="title-bar" flex h-40 aic gap-16>
      <div w-50>资产范围</div>
      <el-button type="primary" @click="show">test</el-button>
      <div flex-1>
        <el-select v-model="category" placeholder="资产类别" filterable clearable>
          <el-option
            v-for="(item, idx) in assetCategories"
            :key="idx"
            :label="makeLabel(item.value, item.label)"
            :value="item.value"
          />
        </el-select>
      </div>
      <div flex-1>
        <el-input
          v-model.trim="searchKeyword"
          placeholder="搜索模板"
          :suffix-icon="Search"
          clearable
        />
      </div>
    </div>
    <div class="data-list">
      <el-checkbox-group v-model="choicesMap[category]">
        <template v-for="(item, idx) in filteredAssets" :key="idx">
          <el-checkbox
            :label="item.label"
            :value="item.value"
            @change="
              v => {
                handleChange(item.value, v);
              }
            "
          />
        </template>
      </el-checkbox-group>
    </div>
  </div>
</template>

<style scoped>
.view-apply-asset-setting {
  background-color: var(--g-block-bg-2);
  .data-list {
    :deep() {
      .el-checkbox {
        width: 150px;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
