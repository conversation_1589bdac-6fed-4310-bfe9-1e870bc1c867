<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { deleteConfirm } from '@/script/interaction';
import { Repos, type RiskTemplate } from '../../../../xtrade-sdk/dist';
import { getUser, remove } from '@/script';

const repoInstance = new Repos.RiskControlRepo();
const riskTemplates = ref<RiskTemplate[]>([]);
const searchKeyword = ref('');
const focusedId = ref<number | null>(null);
const dialogVisible = ref(false);
const newTemplateName = ref('');

const filteredTemplates = computed(() => {
  const kw = (searchKeyword.value || '').toLowerCase();
  return riskTemplates.value.filter(template =>
    template.riskTemplateName.toLowerCase().includes(kw),
  );
});

const emitter = defineEmits<{
  select: [item: RiskTemplate | null];
  toggle: [];
}>();

const handleSelect = (target: RiskTemplate) => {
  focusedId.value = target.id;
  emitter('select', target);
};

const handleAdd = () => {
  newTemplateName.value = '';
  dialogVisible.value = true;
};

async function handleDelete() {
  const tmpls = riskTemplates.value;
  const current = tmpls.find(x => x.id == focusedId.value);
  if (!current) {
    return ElMessage.error('请选择要删除的模板');
  }

  const result = await deleteConfirm('删除模板', `确认删除此模板： ${current.riskTemplateName}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.DeleteTemplate(current.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    remove(tmpls, x => x.id == current.id);
    const target = tmpls.length > 0 ? tmpls[0] : null;
    focusedId.value = target ? target.id : null;
    emitter('select', target);
    ElMessage.success('已删除');
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

const handleToggle = () => {
  emitter('toggle');
};

const handleCancel = () => {
  newTemplateName.value = '';
  dialogVisible.value = false;
};

const handleConfirm = async () => {
  const existingTemplate = riskTemplates.value.find(
    template => template.riskTemplateName === newTemplateName.value,
  );

  if (existingTemplate) {
    ElMessage.error('该风控模板名称已存在');
    return;
  }

  const user = getUser()!;
  const tmpl: RiskTemplate = {
    id: null as any,
    riskTemplateName: newTemplateName.value,
    globalRiskTemplate: false,
    orgId: user.orgId,
    createTime: Date.now(),
    createUserId: user.userId,
    createUserName: user.username,
    updateTime: Date.now(),
  };

  const resp = await repoInstance.CreateTemplate(tmpl);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    newTemplateName.value = '';
    dialogVisible.value = false;
    ElMessage.success('已添加');
    request();
  } else {
    ElMessage.error(`添加失败：${errorCode}/${errorMsg}`);
  }
};

async function request() {
  riskTemplates.value = (await repoInstance.QueryTemplates()).data || [];
}

function getCurrent() {
  return riskTemplates.value.find(x => x.id == focusedId.value);
}

defineExpose({
  getCurrent,
});

onMounted(() => {
  request();
});
</script>

<template>
  <div class="risk-template-list" p-10>
    <div class="search-box" flex aic gap-12>
      <!-- 搜索框 -->
      <el-input
        v-model.trim="searchKeyword"
        placeholder="搜索模板"
        :suffix-icon="Search"
        clearable
      />

      <!-- 添加按钮 -->
      <el-tooltip placement="top" content="添加模板">
        <i class="iconfont icon-add-new" thb1 @click="handleAdd"></i>
      </el-tooltip>

      <!-- 删除按钮 -->
      <el-tooltip placement="top" content="删除模板">
        <i class="iconfont icon-remove" thb1 @click="handleDelete"></i>
      </el-tooltip>

      <!-- 隐藏显示按钮 -->
      <el-tooltip placement="top" content="隐藏|显示该列表">
        <i class="iconfont icon-exchange" thb1 @click="handleToggle"></i>
      </el-tooltip>
    </div>

    <!-- 风控模板列表 -->

    <div class="templite-list" mt-5>
      <template v-for="(template, idx) in filteredTemplates" :key="idx">
        <div
          class="templite-item"
          :class="{ 'is-active': focusedId == template.id }"
          @click="handleSelect(template)"
          h-40
          p-x-14
          p-y-8
        >
          <span fs-14 fw-400>{{ template.riskTemplateName }}</span>
        </div>
      </template>
    </div>

    <!-- 添加模板对话框 -->
    <el-dialog v-model="dialogVisible" title="添加风控模板" width="300px" draggable>
      <el-input v-model.trim="newTemplateName" placeholder="请输入风控模板名称" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.risk-template-list {
  .templite-list {
    .templite-item {
      &:hover,
      &.is-active {
        background-color: var(--g-block-bg-6);
      }
    }
  }
}
</style>
